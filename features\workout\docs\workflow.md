1. 修复模板命名逻辑（P1 - 立即修复）
修改 `TemplateEditSaveHandler.generateDefaultTemplateName()` 方法，实现递增序号命名：

1. **查询现有模板**：通过 Repository 获取所有以"训练模板"开头的模板名称
2. **提取序号**：解析现有模板名称中的数字序号
3. **生成新序号**：找到最大序号并 +1，如果没有则从 1 开始
4. **返回格式**：
   - 第一个模板："训练模板"
   - 后续模板："训练模板1"、"训练模板2"...

**修改文件**：
- `TemplateEditSaveHandler.kt` - generateDefaultTemplateName() 方法
- 添加 `getTemplateRepository()` 依赖注入

**验证步骤**：
- 创建多个模板，确认命名递增
- 删除中间序号模板，确认新建时填补空缺
2. 修复数据持久化问题（P2 - 紧急修复）
修复 `TemplateDataMapper.mapDtoToState()` 方法，确保 customSets 数据正确还原：

1. **JSON 反序列化优化**：
   - 检查 `WorkoutExerciseJson` 中的 customSets 字段映射
   - 确保从 notes 字段中正确解析 customSets JSON 数据
   - 处理向后兼容性（旧数据可能没有 customSets）

2. **State 映射修正**：
   ```kotlin
   // 修改 mapDtoToState 中的动作映射逻辑
   exercises = dto.exercises.map { exercise ->
       TemplateEditContract.ExerciseData(
           // ... 其他字段
           customSets = exercise.customSets ?: reconstructCustomSets(exercise)
       )
   }
   ```

3. **添加数据验证**：
   - 加载后验证 customSets 不为空
   - 记录详细日志用于问题追踪

**修改文件**：
- `TemplateDataMapper.kt` - mapDtoToState() 方法
- `WorkoutExerciseJson.kt` - 确保 customSets 字段正确序列化/反序列化

**验证步骤**：
- 创建包含多组数据的模板
- 保存后重新加载，验证所有组数据完整
- 编辑已有模板，确认数据不丢失
3. 修复用户认证集成（P3 - 重要修复）
完善 `TemplateEditStateManager` 和相关组件的用户ID管理：

1. **初始化时获取用户ID**：
   ```kotlin
   class TemplateEditStateManager @Inject constructor(
       private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase
   ) {
       suspend fun initializeState(): TemplateEditContract.State {
           val userId = getCurrentUserIdUseCase().firstOrNull()?.getOrNull() ?: ""
           return TemplateEditContract.State(currentUserId = userId)
       }
   }
   ```

2. **状态更新时保持用户ID**：
   - 在所有 State copy 操作中保留 currentUserId
   - 确保 Reducer 不会清空用户信息

3. **ViewModel 集成**：
   - 在 ViewModel 初始化时调用 initializeState()
   - 确保用户ID在整个编辑流程中保持一致

**修改文件**：
- `TemplateEditStateManager.kt` - 添加用户ID初始化
- `TemplateEditViewModel.kt` - 集成用户ID获取逻辑
- `TemplateEditReducer.kt` - 确保状态更新时保留用户ID

**验证步骤**：
- 登录后创建模板，验证用户ID正确关联
- 查询模板列表，确认按用户过滤正常工作
4. 集成测试和验证
创建综合测试用例验证所有修复：

1. **端到端测试场景**：
   - 新建模板 → 添加3组数据 → 保存 → 退出 → 重进 → 验证数据完整
   - 编辑已有模板 → 修改组数 → 保存 → 验证更新而非新建
   - 连续创建5个模板 → 验证命名递增正确

2. **单元测试**：
   - `TemplateDataMapperTest` - 测试 customSets 映射
   - `TemplateEditSaveHandlerTest` - 测试命名逻辑
   - `TemplateEditStateManagerTest` - 测试用户ID管理

3. **日志监控**：
   - 添加关键路径日志
   - 监控 `[EMERGENCY-RECOVERY]` 触发频率
   - 验证用户ID在整个流程中的传递

**新增测试文件**：
- `TemplateEditIntegrationTest.kt`
- 更新现有单元测试以覆盖新逻辑
5. 部署和监控
确保修复稳定并建立监控机制：

1. **分阶段发布**：
   - 先发布模板命名修复（低风险）
   - 验证无问题后发布数据持久化修复
   - 最后发布用户认证集成

2. **监控指标**：
   - customSets 数据丢失率
   - 模板保存成功率
   - 用户ID关联正确率

3. **回滚计划**：
   - 保留原始代码分支
   - 准备快速回滚脚本
   - 建立问题反馈渠道

**监控工具**：
- Firebase Crashlytics 自定义事件
- 应用内日志收集
- 用户反馈系统
